using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Pages;

public partial class StudentsBase : ComponentBase
{
    [Inject] protected IStudentApiService StudentApi { get; set; } = default!;
    [Inject] protected ITutorApiService TutorApi { get; set; } = default!;
    [Inject] protected ISubjectApiService SubjectApi { get; set; } = default!;
    [Inject] protected ILessonApiService LessonApi { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IDialogService DialogService { get; set; } = default!;

    // Data properties
    protected List<Student> students = new();
    protected List<Tutor> tutors = new();
    protected List<Subject> subjects = new();
    protected bool isLoading = true;
    protected bool showModal = false;
    protected bool isEditMode = false;
    protected bool isSaving = false;
    protected string modalTitle = "";
    protected Student currentStudent = new();
    protected string currentStudentSubjectName = "";
    protected string currentStudentTutorName = "";
    protected bool showSubjectValidation = false;
    protected bool showTutorValidation = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    protected async Task LoadData()
    {
        isLoading = true;
        try
        {
            var studentsTask = StudentApi.GetStudentsAsync();
            var tutorsTask = TutorApi.GetTutorsAsync();
            var subjectsTask = SubjectApi.GetSubjectsAsync();

            await Task.WhenAll(studentsTask, tutorsTask, subjectsTask);

            students = await studentsTask;
            tutors = await tutorsTask;
            subjects = await subjectsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {students.Count} students, {tutors.Count} tutors, {subjects.Count} subjects");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading data", ex.Message);
        }
        finally
        {
            isLoading = false;
        }
    }

    protected async Task RefreshData()
    {
        await LoadData();
    }

    protected void OpenCreateModal()
    {
        currentStudent = new Student();
        currentStudentSubjectName = "";
        currentStudentTutorName = "";
        showSubjectValidation = false;
        showTutorValidation = false;
        isEditMode = false;
        modalTitle = "Create New Student";
        showModal = true;
    }

    protected void OpenEditModal(Student? student)
    {
        if (student != null)
        {
            currentStudent = new Student
            {
                StudentId = student.StudentId,
                StudentName = student.StudentName,
                Email = student.Email,
                TutorID = student.TutorID,
                SubjectId = student.SubjectId
            };

            // Set the subject name for readonly display
            var subject = subjects.FirstOrDefault(s => s.SubjectId == student.SubjectId);
            currentStudentSubjectName = subject?.SubjectName ?? "No subject assigned";

            // Set the tutor name for readonly display
            var tutor = tutors.FirstOrDefault(t => t.TutorId == student.TutorID);
            currentStudentTutorName = tutor?.TutorName ?? "No tutor assigned";

            isEditMode = true;
            modalTitle = "Edit Student";
            showModal = true;
        }
    }

    protected void CloseModal()
    {
        showModal = false;
        currentStudent = new();
        currentStudentSubjectName = "";
        currentStudentTutorName = "";
        showSubjectValidation = false;
        showTutorValidation = false;
        isSaving = false;
    }

    protected async Task SaveStudent()
    {
        // Reset validation flags
        showSubjectValidation = false;
        showTutorValidation = false;

        if (string.IsNullOrWhiteSpace(currentStudent.StudentName))
        {
            await DialogService.ShowWarningAsync("Student name is required.", "Please enter a valid student name before saving.");
            return;
        }

        // Validate subject and tutor for new students only
        if (!isEditMode)
        {
            bool hasValidationErrors = false;

            if (!currentStudent.SubjectId.HasValue || currentStudent.SubjectId <= 0)
            {
                showSubjectValidation = true;
                hasValidationErrors = true;
            }

            if (!currentStudent.TutorID.HasValue || currentStudent.TutorID <= 0)
            {
                showTutorValidation = true;
                hasValidationErrors = true;
            }

            if (hasValidationErrors)
            {
                StateHasChanged();
                return;
            }
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditMode)
            {
                success = await StudentApi.UpdateStudentAsync(currentStudent.StudentId, currentStudent);
            }
            else
            {
                var createdStudent = await StudentApi.CreateStudentAsync(currentStudent);
                success = createdStudent != null;
            }

            if (success)
            {
                CloseModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save student", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving student: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving student", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteStudent(Student? student)
    {
        if (student == null) return;

        try
        {
            // Check if student has future lessons
            var lessons = await LessonApi.GetLessonsAsync();
            var futureLessons = lessons.Where(l => l.StudentId == student.StudentId && l.StartTime > DateTime.Now).ToList();

            if (futureLessons.Any())
            {
                var warningMessage = $"Cannot delete student '{student.StudentName}' because they have {futureLessons.Count} upcoming lesson(s).";
                var warningDetails = "Please cancel or reschedule their future lessons first.";
                await DialogService.ShowWarningAsync(warningMessage, warningDetails, "Cannot Delete Student");
                return;
            }

            var message = $"Are you sure you want to delete student '{student.StudentName}'?";
            var details = "This action cannot be undone.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Student");

            if (confirmed)
            {
                var success = await StudentApi.DeleteStudentAsync(student.StudentId);
                if (success)
                {
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete student", "Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting student: {ex.Message}");
            await DialogService.ShowErrorAsync("Error deleting student", ex.Message);
        }
    }

    protected async Task ToggleArchiveStatus(Student? student)
    {
        if (student == null) return;

        try
        {
            student.IsArchived = !student.IsArchived;
            var success = await StudentApi.UpdateStudentAsync(student.StudentId, student);
            
            if (success)
            {
                await LoadData();
            }
            else
            {
                // Revert the change if update failed
                student.IsArchived = !student.IsArchived;
                await DialogService.ShowErrorAsync("Failed to update student status", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            // Revert the change if update failed
            student.IsArchived = !student.IsArchived;
            await JSRuntime.InvokeVoidAsync("console.error", $"Error updating student status: {ex.Message}");
            await DialogService.ShowErrorAsync("Error updating student status", ex.Message);
        }
    }

    protected string GetSubjectName(int? subjectId)
    {
        if (!subjectId.HasValue) return "No Subject";
        var subject = subjects.FirstOrDefault(s => s.SubjectId == subjectId.Value);
        return subject?.SubjectName ?? "Unknown Subject";
    }

    protected string GetArchiveButtonText(bool isArchived)
    {
        return isArchived ? "Unarchive" : "Archive";
    }

    protected string GetArchiveButtonClass(bool isArchived)
    {
        return isArchived ? "btn-outline-success" : "btn-outline-warning";
    }

    protected string GetArchiveButtonIcon(bool isArchived)
    {
        return isArchived ? "bi-arrow-up-circle" : "bi-archive";
    }

    protected void ViewLessons(Student? student)
    {
        if (student != null)
        {
            // Navigate to lessons page with student filter
            Navigation.NavigateTo($"/lessons?studentId={student.StudentId}");
        }
    }
}
