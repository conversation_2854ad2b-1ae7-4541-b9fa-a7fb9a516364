using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Enums;
using Syncfusion.Blazor.Schedule;

namespace ShiningCMusicApp.Pages;

public partial class LessonsBase
{
    protected async Task ShowCustomQuickInfo(ScheduleEvent lesson)
    {
        // Create HTML content that exactly matches the existing QuickInfo template
        var quickInfoHtml = $@"
            <div class='quick-info' style='font-family: -apple-system, BlinkMacSystemFont, ""Segoe UI"", Roboto, sans-serif; padding: 0;'>
                <div class='event-title' style='font-size: 16px; font-weight: 600; margin-bottom: 12px; color: #333;'>{lesson.SubjectName}</div>
                <div class='event-details'>
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-clock' style='margin-right: 8px; color: #666;'></i>
    {lesson.StartTime:MMM dd, yyyy h:mm tt} - {lesson.EndTime:h:mm tt}
                    </p>";

        if (!string.IsNullOrEmpty(lesson.TutorName))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-person-fill' style='margin-right: 8px; color: #666;'></i>
                        <strong>Tutor:</strong>&nbsp;{lesson.TutorName}
                    </p>";
        }

        if (!string.IsNullOrEmpty(lesson.StudentName))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-person' style='margin-right: 8px; color: #666;'></i>
                        <strong>Student:</strong>&nbsp;{lesson.StudentName}
                    </p>";
        }

        if (!string.IsNullOrEmpty(lesson.Location))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-geo-alt' style='margin-right: 8px; color: #666;'></i>
                        <strong>Location:</strong>&nbsp;{lesson.Location}
                    </p>";
        }

        if (!string.IsNullOrEmpty(lesson.Description))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: flex-start;'>
                        <i class='bi bi-card-text' style='margin-right: 8px; color: #666; margin-top: 2px;'></i>
                        <strong>Description:</strong>&nbsp;{lesson.Description}
                    </p>";
        }

        // Add edit and delete buttons for admin users
        if (CanEditEvents)
        {
            quickInfoHtml += $@"
                    <div style='margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee; display: flex; gap: 8px; justify-content: flex-end;'>
                        <button onclick='editLesson({lesson.Id})' style='
                            background: transparent;
                            color: #0066cc;
                            border: 1px solid #0066cc;
                            padding: 8px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 16px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.2s;
                            width: 40px;
                            min-width: 40px;
                            height: 40px;
                        ' onmouseover='this.style.backgroundColor=""#0066cc""; this.style.color=""white""' onmouseout='this.style.backgroundColor=""transparent""; this.style.color=""#0066cc""'>
                            <i class='bi bi-pencil' style='line-height: 1; margin: 0 auto; color: inherit;'></i>
                        </button>
                        <button onclick='deleteLesson({lesson.Id})' style='
                            background: transparent;
                            color: #dc3545;
                            border: 1px solid #dc3545;
                            padding: 8px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 16px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.2s;
                            width: 40px;
                            min-width: 40px;
                            height: 40px;
                        ' onmouseover='this.style.backgroundColor=""#dc3545""; this.style.color=""white""' onmouseout='this.style.backgroundColor=""transparent""; this.style.color=""#dc3545""'>
                            <i class='bi bi-trash' style='line-height: 1; margin: 0 auto; color: inherit;'></i>
                        </button>
                    </div>";
        }

        quickInfoHtml += @"
                </div>
            </div>";

        // Show the custom quick info popup
        await JSRuntime.InvokeVoidAsync("showCustomQuickInfo", quickInfoHtml, lesson.SubjectName);
    }

    // ICS Import/Export Methods
    protected async Task OnExportToIcs()
    {
        try
        {
            if (scheduleRef == null) return;

            // Get events to export based on user role
            var eventsToExport = GetEventsForExport();

            if (!eventsToExport.Any())
            {
                await DialogService.ShowWarningAsync("No events to export", "There are no events available for export.");
                return;
            }

            // Generate filename based on user role and current date
            var fileName = GenerateExportFileName();

            // Export using Syncfusion's built-in method
            await scheduleRef.ExportToICalendarAsync(fileName, eventsToExport);

            await DialogService.ShowSuccessAsync("Export Successful", $"Calendar events have been exported to {fileName}.ics");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error exporting to ICS: {ex.Message}");
            await DialogService.ShowErrorAsync("Export Failed", "An error occurred while exporting the calendar. Please try again.");
        }
    }

    protected async Task OnFileInputChange(ChangeEventArgs e)
    {
        try
        {
            var files = e.Value as string;
            if (string.IsNullOrEmpty(files)) return;

            // Use JavaScript to read the file content
            await JSRuntime.InvokeVoidAsync("handleIcsFileImport", "icsFileInput");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error handling file input: {ex.Message}");
            await DialogService.ShowErrorAsync("Import Failed", "An error occurred while processing the file. Please try again.");
        }
    }

    [JSInvokable]
    public async Task ProcessIcsFile(string fileName, string fileContent)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(fileContent))
            {
                await DialogService.ShowWarningAsync("Empty File", "The selected file appears to be empty.");
                return;
            }

            if (!fileName.EndsWith(".ics", StringComparison.OrdinalIgnoreCase))
            {
                await DialogService.ShowWarningAsync("Invalid File Type", "Please select a valid ICS calendar file.");
                return;
            }

            // Import using Syncfusion's built-in method
            if (scheduleRef != null)
            {
                await scheduleRef.ImportICalendarAsync(fileContent);
                await LoadData(); // Refresh the data to show imported events
                await DialogService.ShowSuccessAsync("Import Successful", $"Calendar events have been imported from {fileName}");

                // Clear the file input for next use
                await JSRuntime.InvokeVoidAsync("clearFileInput", "icsFileInput");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error importing ICS file: {ex.Message}");
            await DialogService.ShowErrorAsync("Import Failed", "An error occurred while importing the calendar file. Please check the file format and try again.");
        }
    }

    protected List<ScheduleEvent> GetEventsForExport()
    {
        // Return events - already filtered by user role in LoadData()
        return scheduleEvents.ToList();
    }

    protected string GenerateExportFileName()
    {
        var currentUser = authState?.User;
        var timestamp = DateTime.Now.ToString("yyyy-MM-dd");

        if (currentUser?.IsInRole(UserRoleEnum.Administrator.ToString()) == true)
        {
            return $"ShiningCMusic_AllLessons_{timestamp}";
        }
        else if (currentUser?.IsInRole(UserRoleEnum.Tutor.ToString()) == true)
        {
            return $"ShiningCMusic_MyLessons_Tutor_{timestamp}";
        }
        else if (currentUser?.IsInRole(UserRoleEnum.Student.ToString()) == true)
        {
            return $"ShiningCMusic_MyLessons_Student_{timestamp}";
        }
        else
        {
            return $"ShiningCMusic_Lessons_{timestamp}";
        }
    }

    // Add method to expand recurring events
    protected List<ScheduleEvent> GetExpandedEvents(DateTime startDate, DateTime endDate)
    {
        var expandedEvents = new List<ScheduleEvent>();

        foreach (var evt in scheduleEvents)
        {
            if (string.IsNullOrEmpty(evt.RecurrenceRule))
            {
                // Non-recurring event - add if within range
                if (evt.StartTime >= startDate && evt.StartTime <= endDate)
                {
                    expandedEvents.Add(evt);
                }
            }
            else
            {
                // Recurring event - expand occurrences using RecurrenceHelper
                // Calculate maximum count based on date range to avoid infinite generation
                var daysDiff = (endDate - startDate).Days;
                var maxCount = Math.Max(100, daysDiff * 2); // Reasonable limit based on date range

                var occurrences = RecurrenceHelper.GetRecurrenceDateTimeCollection(
                    evt.RecurrenceRule,
                    evt.RecurrenceException ?? string.Empty,
                    1, // firstDayOfWeek (Monday = 1)
                    evt.StartTime,
                    null, // recurrenceEndDate
                    startDate, // dateRangeStart
                    endDate, // dateRangeEnd
                    maxCount // maximumCount
                );

                // Filter occurrences to only include those within the date range
                foreach (var occurrence in occurrences.Where(o => o >= startDate && o <= endDate))
                {
                    var expandedEvent = new ScheduleEvent
                    {
                        Id = evt.Id,
                        Subject = evt.Subject,
                        StartTime = occurrence,
                        EndTime = occurrence.Add(evt.EndTime - evt.StartTime),
                        Description = evt.Description,
                        Location = evt.Location,
                        TutorId = evt.TutorId,
                        StudentId = evt.StudentId,
                        SubjectId = evt.SubjectId,
                        LocationId = evt.LocationId,
                        TutorName = evt.TutorName,
                        StudentName = evt.StudentName,
                        SubjectName = evt.SubjectName,
                        LocationName = evt.LocationName,
                        CategoryColor = evt.CategoryColor,
                        RecurrenceRule = evt.RecurrenceRule,
                        RecurrenceID = evt.RecurrenceID,
                        RecurrenceException = evt.RecurrenceException
                    };
                    expandedEvents.Add(expandedEvent);
                }
            }
        }

        return expandedEvents.OrderBy(e => e.StartTime).ToList();
    }

    // Add property to get the next lesson
    protected ScheduleEvent? NextLesson
    {
        get
        {
            var now = DateTime.Now;
            var endDate = now.AddMonths(3); // Look ahead 3 months
            var expandedEvents = GetExpandedEvents(now, endDate);

            return expandedEvents
                .Where(e => e.StartTime > now)
                .OrderBy(e => e.StartTime)
                .FirstOrDefault();
        }
    }
}
