using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using Syncfusion.Blazor;
using System.Net.Http.Json;
using ShiningCMusicCommon.Models;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Load configuration with simplified logic
var appConfig = await LoadConfigurationAsync(builder);

// Register Syncfusion license
if (!string.IsNullOrEmpty(appConfig.SyncfusionLicense))
{
    Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(appConfig.SyncfusionLicense);
}

Console.WriteLine($"Configuration loaded - API: {appConfig.ApiBaseUrl}, Session timeout: {appConfig.SessionTimeoutMinutes} minutes");

builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Add HttpClient
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// Register configuration services
builder.Services.AddSingleton(appConfig);
builder.Services.AddScoped<ApiConfiguration>(_ => new ApiConfiguration { BaseUrl = appConfig.ApiBaseUrl });

// Add authentication services
builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<CustomAuthenticationStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthenticationStateProvider>());

// Add session timeout service with configured timeout minutes
builder.Services.AddScoped<ISessionTimeoutService>(provider =>
    new SessionTimeoutService(
        provider.GetRequiredService<IJSRuntime>(),
        provider.GetRequiredService<CustomAuthenticationStateProvider>(),
        provider.GetRequiredService<NavigationManager>(),
        appConfig.SessionTimeoutMinutes));

// Add our services
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<IDialogService, DialogService>();

// Add individual API services
builder.Services.AddScoped<ILessonApiService, LessonApiService>();
builder.Services.AddScoped<ITutorApiService, TutorApiService>();
builder.Services.AddScoped<IStudentApiService, StudentApiService>();
builder.Services.AddScoped<ISubjectApiService, SubjectApiService>();
builder.Services.AddScoped<ILocationApiService, LocationApiService>();
builder.Services.AddScoped<IUserApiService, UserApiService>();

// Add Syncfusion Blazor service
builder.Services.AddSyncfusionBlazor();

await builder.Build().RunAsync();

// Configuration loading helper method
static async Task<AppConfiguration> LoadConfigurationAsync(WebAssemblyHostBuilder builder)
{
    // Load local configuration once
    var localHttp = new HttpClient() { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    Dictionary<string, string>? localConfig = null;

    try
    {
        localConfig = await localHttp.GetFromJsonAsync<Dictionary<string, string>>("appsettings.json");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Failed to load local configuration: {ex.Message}");
    }

    // Determine API base URL
    var apiBaseUrl = DetermineApiBaseUrl(builder, localConfig);

    // Try to load server configuration
    var serverConfig = await TryLoadServerConfigurationAsync(apiBaseUrl);

    // Build final configuration with fallback chain: Server → Local → Defaults
    return BuildFinalConfiguration(serverConfig, localConfig, apiBaseUrl);
}

static string DetermineApiBaseUrl(WebAssemblyHostBuilder builder, Dictionary<string, string>? localConfig)
{
    if (builder.HostEnvironment.BaseAddress.Contains("azurewebsites.net"))
    {
        // Azure deployment - use same domain
        var baseUri = new Uri(builder.HostEnvironment.BaseAddress);
        return $"{baseUri.Scheme}://{baseUri.Host}/api";
    }

    // Local development - use local config or default
    return localConfig?.GetValueOrDefault("ApiBaseUrl") ?? "https://localhost:7268/api";
}

static async Task<AppConfiguration?> TryLoadServerConfigurationAsync(string apiBaseUrl)
{
    try
    {
        var configurationEndpoint = $"{apiBaseUrl}/configuration";
        Console.WriteLine($"Attempting to load configuration from: {configurationEndpoint}");

        using var http = new HttpClient();
        var serverConfig = await http.GetFromJsonAsync<AppConfiguration>(configurationEndpoint);

        if (serverConfig != null && !string.IsNullOrEmpty(serverConfig.ApiBaseUrl))
        {
            Console.WriteLine("Successfully loaded server configuration");
            return serverConfig;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Failed to load server configuration: {ex.Message}");
    }

    return null;
}

static AppConfiguration BuildFinalConfiguration(AppConfiguration? serverConfig, Dictionary<string, string>? localConfig, string fallbackApiBaseUrl)
{
    // Use server config if available
    if (serverConfig != null)
    {
        return serverConfig;
    }

    // Fallback to local config with defaults
    var config = new AppConfiguration
    {
        ApiBaseUrl = fallbackApiBaseUrl,
        SyncfusionLicense = localConfig?.GetValueOrDefault("SyncfusionLicense") ?? string.Empty,
        SessionTimeoutMinutes = 30 // Default
    };

    // Parse session timeout from local config
    if (localConfig?.TryGetValue("SessionTimeoutMinutes", out var timeoutStr) == true)
    {
        if (int.TryParse(timeoutStr, out var timeout))
        {
            config.SessionTimeoutMinutes = timeout;
        }
    }

    Console.WriteLine("Using local configuration with defaults");
    return config;
}
