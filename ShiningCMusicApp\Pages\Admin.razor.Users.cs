using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Utilities;
using Syncfusion.Blazor.DropDowns;

namespace ShiningCMusicApp.Pages;

public partial class AdminBase
{
    // User Management Methods
    protected void OpenCreateUserModal()
    {
        currentUser = new User();
        selectedTutorId = null;
        selectedStudentId = null;
        showAssignmentSection = false;
        passwordValidationErrors.Clear();
        isEditUserMode = false;
        userModalTitle = "Create New User";
        showUserModal = true;
    }

    protected void OpenEditUserModal(User? user)
    {
        if (user != null)
        {
            currentUser = new User
            {
                LoginName = user.LoginName,
                UserName = user.UserName,
                Password = "", // Clear password for edit mode
                Note = user.Note,
                RoleId = user.RoleId
            };
            passwordValidationErrors.Clear();
            isEditUserMode = true;
            userModalTitle = "Edit User";
            showUserModal = true;
        }
    }

    protected void CloseUserModal()
    {
        showUserModal = false;
        currentUser = new();
        selectedTutorId = null;
        selectedStudentId = null;
        showAssignmentSection = false;
        passwordValidationErrors.Clear();
        isSaving = false;
    }

    protected string GetPasswordPlaceholder()
    {
        return isEditUserMode ? "Leave blank to keep current password" : "Enter password (min 8 characters)";
    }

    protected void ValidatePassword()
    {
        passwordValidationErrors.Clear();

        // Only validate if password is provided
        if (!string.IsNullOrEmpty(currentUser.Password))
        {
            passwordValidationErrors = PasswordValidator.GetPasswordErrors(currentUser.Password);
        }
        else if (!isEditUserMode)
        {
            // Password is required for new users
            passwordValidationErrors.Add("Password is required for new users.");
        }

        StateHasChanged();
    }

    protected void OnRoleChanged(ChangeEventArgs<int?, UserRole> args)
    {
        if (!isEditUserMode)
        {
            var roleDescription = GetSelectedRoleDescription();
            showAssignmentSection = roleDescription == UserRoleEnum.Tutor.ToString() || roleDescription == UserRoleEnum.Student.ToString();
            selectedTutorId = null;
            selectedStudentId = null;
        }
    }

    protected string GetSelectedRoleDescription()
    {
        if (currentUser.RoleId.HasValue)
        {
            var role = userRoles.FirstOrDefault(r => r.ID == currentUser.RoleId.Value);
            return role?.Description ?? "";
        }
        return "";
    }

    protected List<Tutor> GetAvailableTutors()
    {
        // Return tutors that don't have a login name assigned yet
        return tutors.Where(t => string.IsNullOrEmpty(t.LoginName)).ToList();
    }

    protected List<Student> GetAvailableStudents()
    {
        // Return students that don't have a login name assigned yet
        return students.Where(s => string.IsNullOrEmpty(s.LoginName)).ToList();
    }

    protected async Task SaveUser()
    {
        isSaving = true;
        try
        {
            // Validate password before saving
            ValidatePassword();

            // Check if there are validation errors
            if (passwordValidationErrors.Any())
            {
                var errorDetails = string.Join("\n", passwordValidationErrors);
                await DialogService.ShowWarningAsync("Password validation failed", errorDetails, "Validation Error");
                return;
            }

            // For new users, password is required
            if (!isEditUserMode && string.IsNullOrEmpty(currentUser.Password))
            {
                await DialogService.ShowWarningAsync("Password is required for new users.", "Please enter a password before creating the user.");
                return;
            }

            if (isEditUserMode)
            {
                await UserApi.UpdateUserAsync(currentUser.LoginName, currentUser);
            }
            else
            {
                // Create the user first
                var createdUser = await UserApi.CreateUserAsync(currentUser);
                if (createdUser != null)
                {
                    // Handle assignment to tutor or student
                    await HandleUserAssignment();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to create user", "Please try again.");
                    return;
                }
            }

            CloseUserModal();
            await LoadData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving user: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving user", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task HandleUserAssignment()
    {
        try
        {
            if (selectedTutorId.HasValue)
            {
                // Update the tutor's login name
                var tutor = tutors.FirstOrDefault(t => t.TutorId == selectedTutorId.Value);
                if (tutor != null)
                {
                    tutor.LoginName = currentUser.LoginName;
                    await TutorApi.UpdateTutorAsync(tutor.TutorId, tutor);
                }
            }
            else if (selectedStudentId.HasValue)
            {
                // Update the student's login name
                var student = students.FirstOrDefault(s => s.StudentId == selectedStudentId.Value);
                if (student != null)
                {
                    student.LoginName = currentUser.LoginName;
                    await StudentApi.UpdateStudentAsync(student.StudentId, student);
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error assigning user: {ex.Message}");
            // Don't throw here as the user was already created successfully
        }
    }

    protected async Task DeleteUser(string? loginName)
    {
        if (!string.IsNullOrEmpty(loginName))
        {
            var message = $"Are you sure you want to delete user '{loginName}'?";
            var details = "This action cannot be undone.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete User");
            if (!confirmed) return;

            try
            {
                await UserApi.DeleteUserAsync(loginName);
                await LoadData();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting user: {ex.Message}");
                await DialogService.ShowErrorAsync("Error deleting user", ex.Message);
            }
        }
    }

    // UserRole Management Methods
    protected void OpenCreateUserRoleModal()
    {
        currentUserRole = new UserRole
        {
            ID = GetNextAvailableRoleId()
        };
        isEditUserRoleMode = false;
        userRoleModalTitle = "Create New User Role";
        showUserRoleModal = true;
    }

    protected int GetNextAvailableRoleId()
    {
        if (!userRoles.Any())
            return 1;

        // Find the next available ID starting from 1
        for (int i = 1; i <= 999; i++)
        {
            if (!userRoles.Any(r => r.ID == i))
                return i;
        }

        // If all IDs 1-999 are taken, return the max + 1
        return userRoles.Max(r => r.ID) + 1;
    }

    protected void OpenEditUserRoleModal(UserRole? role)
    {
        if (role != null)
        {
            currentUserRole = new UserRole
            {
                ID = role.ID,
                Description = role.Description
            };
            isEditUserRoleMode = true;
            userRoleModalTitle = "Edit User Role";
            showUserRoleModal = true;
        }
    }

    protected void CloseUserRoleModal()
    {
        showUserRoleModal = false;
        currentUserRole = new();
        isSaving = false;
    }

    protected async Task SaveUserRole()
    {
        isSaving = true;
        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(currentUserRole.Description))
            {
                await DialogService.ShowWarningAsync("Role description is required.", "Please enter a valid role description before saving.");
                return;
            }

            if (currentUserRole.ID <= 0)
            {
                await DialogService.ShowWarningAsync("Role ID must be greater than 0.", "Please enter a valid role ID before saving.");
                return;
            }

            // Check for duplicate ID when creating
            if (!isEditUserRoleMode && userRoles.Any(r => r.ID == currentUserRole.ID))
            {
                await DialogService.ShowWarningAsync("Role ID already exists.", $"A role with ID {currentUserRole.ID} already exists. Please choose a different ID.");
                return;
            }

            bool success;
            if (isEditUserRoleMode)
            {
                success = await UserApi.UpdateUserRoleAsync(currentUserRole);
            }
            else
            {
                var createdRole = await UserApi.CreateUserRoleAsync(currentUserRole);
                success = createdRole != null;
            }

            if (success)
            {
                CloseUserRoleModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save user role", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving user role: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving user role", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteUserRole(int? roleId)
    {
        if (roleId.HasValue)
        {
            var role = userRoles.FirstOrDefault(r => r.ID == roleId.Value);
            if (role == null) return;

            var message = $"Are you sure you want to delete role '{role.Description}'?";
            var details = "This action cannot be undone.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete User Role");
            if (!confirmed) return;

            try
            {
                await UserApi.DeleteUserRoleAsync(roleId.Value);
                await LoadData();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting user role: {ex.Message}");
                await DialogService.ShowErrorAsync("Error deleting user role", ex.Message);
            }
        }
    }
}
